[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "workers-py"
version = "0.1.0"
description = "Python AI workers service for document processing"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    { name = "Ledgerly Team", email = "<EMAIL>" },
]
license = { text = "MIT" }
keywords = ["ai", "document-processing", "ocr", "pdf", "workers"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
    "httpx>=0.25.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pytesseract>=0.3.10",
    "pdfplumber>=0.10.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "pgvector>=0.2.0",
    "sentence-transformers>=2.2.0",
    "psycopg2-binary>=2.9.0",
    "asyncpg>=0.29.0",
    "pillow>=10.0.0",
    "python-multipart>=0.0.6",
    "pdf2image>=1.17.0",
    "paramiko>=3.4.0",
    "email-validator>=2.1.0",
    "openai>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "black>=23.0.0",
    "mypy>=1.7.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "pandas-stubs>=2.0.0.240605",
]

[project.urls]
Homepage = "https://github.com/Jpkay/ledgerly"
Repository = "https://github.com/Jpkay/ledgerly"
Documentation = "https://docs.belbooks.com"

[project.scripts]
workers-py = "workers_py.main:app"

[tool.hatch.build.targets.wheel]
packages = ["src/workers_py"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/tests",
    "/README.md",
]

[tool.ruff]
target-version = "py311"
line-length = 88
exclude = [
    ".venv",
    "venv",
    ".lint-venv",
    "node_modules",
    "build",
    "dist",
    "__pycache__",
]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
"tests/**" = [
    "I001",  # import sort/format in tests not enforced
    "W292",  # trailing newline
    "W293",  # blank line contains whitespace
    "W291",  # trailing whitespace
    "F401",  # unused imports in tests
    "UP035", # typing -> collections.abc migration
    "B017",  # broad exception with pytest.raises in tests
]

[tool.black]
target-version = ["py311"]
line-length = 88

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_optional = true
files = [
  "src/workers_py/main.py",
  "src/workers_py/models.py",
  "src/workers_py/services/pdf.py",
]
ignore_missing_imports = true
exclude = "(?x)(^tests/|src/workers_py/services/(ocr|delivery|suggest|extract|ai_suggest)\\.py$|src/workers_py/database\\.py$)"
follow_imports = "skip"

[[tool.mypy.overrides]]
module = [
  "pdfplumber",
  "pdfplumber.*",
]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
  "workers_py.services.ai",
  "workers_py.tasks",
  "workers_py.suggest_task",
]
ignore_errors = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --cov=src"
testpaths = ["tests"]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
]
