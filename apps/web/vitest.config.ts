import { defineConfig } from 'vitest/config'
import path from 'node:path'

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname),
      '@/lib': path.resolve(__dirname, 'lib'),
      '@/app': path.resolve(__dirname, 'app'),
      '@/contexts': path.resolve(__dirname, 'contexts'),
      '@/hooks': path.resolve(__dirname, 'hooks'),
      '@/components': path.resolve(__dirname, 'components'),
      '@belbooks/types': path.resolve(
        __dirname,
        '../../packages/types/src/index.ts'
      ),
      '@belbooks/types/*': path.resolve(__dirname, '../../packages/types/src'),
      '@belbooks/dal': path.resolve(
        __dirname,
        '../../packages/dal/src/index.ts'
      ),
      '@belbooks/dal/*': path.resolve(__dirname, '../../packages/dal/src'),
      '@belbooks/import-service': path.resolve(
        __dirname,
        '../../packages/import-service/src/index.ts'
      ),
      '@ledgerly/import-service/*': path.resolve(
        __dirname,
        '../../packages/import-service/src'
      ),
      '@ledgerly/domain-invoicing': path.resolve(
        __dirname,
        '../../packages/domain-invoicing/src/index.ts'
      ),
      '@ledgerly/domain-invoicing/*': path.resolve(
        __dirname,
        '../../packages/domain-invoicing/src'
      ),
      '@ledgerly/domain-bank': path.resolve(
        __dirname,
        '../../packages/domain-bank/src/index.ts'
      ),
      '@ledgerly/domain-bank/*': path.resolve(
        __dirname,
        '../../packages/domain-bank/src'
      ),
    },
  },
  esbuild: {
    // Ensure TSX tests compile with the automatic JSX runtime so React import isn't required
    jsx: 'automatic',
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.global.ts', './vitest.setup.ts'],
    testTimeout: 30000,
    hookTimeout: 15000,
    // Use process pool to avoid worker OOMs in CI
    pool: 'forks',
    poolOptions: { forks: { singleFork: true } },
    maxConcurrency: 3, // Reduced for better memory management
    logHeapUsage: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'text-summary', 'lcov'],
      reportsDirectory: './coverage',
      all: false,
    },
    include: ['__tests__/**/*.{test,spec}.{ts,tsx}'],
  },
})
